<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Influencer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class ComputeAverageReachJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private int $influencerId)
    {
    }

    public function handle(): void
    {
        $influencer = Influencer::find($this->influencerId);
        if (!$influencer) {
            return;
        }

        $since = Carbon::now()->subDays(60);
        $postTypes = [
            'story' => ['threshold' => 15, 'field' => 'ig_story_avg_reach', 'flag' => 'flag_story_insight_missing'],
            'reel'  => ['threshold' => 5,  'field' => 'ig_reel_avg_reach',  'flag' => 'flag_reel_insight_missing'],
            'feed'  => ['threshold' => 5,  'field' => 'ig_feed_avg_reach',  'flag' => 'flag_feed_insight_missing'],
        ];

        foreach ($postTypes as $type => $meta) {
            $reaches = $influencer->igPosts()
                ->where('post_type', $type)
                ->where('posted_at', '>=', $since)
                ->pluck('reach')
                ->sort()
                ->values();

            if ($reaches->count() < $meta['threshold']) {
                $influencer->{$meta['field']} = null;
                $influencer->{$meta['flag']} = true;
                continue;
            }

            $count = $reaches->count();
            $trim = (int) floor($count * 0.2);
            if ($trim > 0) {
                $reaches = $reaches->slice($trim, $count - (2 * $trim));
            }
            $average = (int) round($reaches->avg());
            $influencer->{$meta['field']} = $average;
            $influencer->{$meta['flag']} = false;
        }

        $influencer->avg_reach_computed_at = Carbon::now();
        $influencer->save();
    }
}

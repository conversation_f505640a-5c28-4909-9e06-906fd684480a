<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Influencer;
use App\Models\InfluencerPrice;
use App\Services\PricingCalculator;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class RepriceAllJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(PricingCalculator $calculator): void
    {
        Influencer::chunkById(500, function ($influencers) use ($calculator) {
            foreach ($influencers as $inf) {
                try {
                    ComputeAverageReachJob::dispatch($inf->id);

                    foreach ([
                        'Boost Me',
                        'Reaction Video',
                        'Survey',
                    ] as $campaignType) {
                        $data = $calculator->calculatePrice($inf, $campaignType);

                        InfluencerPrice::updateOrCreate(
                            ['influencer_id' => $inf->id, 'campaign_type' => $campaignType],
                            [
                                'post_type' => $data['breakdown']['post_type'],
                                'price' => $data['price'],
                                'breakdown' => $data['breakdown'],
                                'priced_at' => now(),
                            ]
                        );
                    }
                } catch (Throwable $e) {
                    Log::error('Reprice failed for influencer '.$inf->id.': '.$e->getMessage());
                }
            }
        });
    }
}

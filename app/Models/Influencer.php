<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Support\CampaignPostType;

/**
 * @property int $followers
 * @property float $gamification_percentage
 * @property int|null $ig_story_avg_reach
 * @property int|null $ig_reel_avg_reach
 * @property int|null $ig_feed_avg_reach
 * @property bool $flag_story_insight_missing
 * @property bool $flag_reel_insight_missing
 * @property bool $flag_feed_insight_missing
 * @property bool $admin_override_show_hidden
 */
class Influencer extends Model
{
    use HasFactory;

    protected $fillable = [
        'followers',
        'gamification_percentage',
        'ig_story_avg_reach',
        'ig_reel_avg_reach',
        'ig_feed_avg_reach',
        'flag_story_insight_missing',
        'flag_reel_insight_missing',
        'flag_feed_insight_missing',
        'admin_override_show_hidden',
        'avg_reach_computed_at',
    ];

    protected $casts = [
        'gamification_percentage' => 'float',
        'flag_story_insight_missing' => 'boolean',
        'flag_reel_insight_missing' => 'boolean',
        'flag_feed_insight_missing' => 'boolean',
        'admin_override_show_hidden' => 'boolean',
        'avg_reach_computed_at' => 'datetime',
    ];

    public function igPosts(): HasMany
    {
        return $this->hasMany(IgPost::class);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(InfluencerPrice::class);
    }

    public function scopeVisibleForCampaign($query, string $campaignType)
    {
        $postType = CampaignPostType::forCampaignType($campaignType);
        $flagColumn = match ($postType) {
            'story' => 'flag_story_insight_missing',
            'reel' => 'flag_reel_insight_missing',
            'feed' => 'flag_feed_insight_missing',
        };

        return $query->where(function ($q) use ($flagColumn) {
            $q->where('admin_override_show_hidden', true)
              ->orWhere($flagColumn, false);
        });
    }
}

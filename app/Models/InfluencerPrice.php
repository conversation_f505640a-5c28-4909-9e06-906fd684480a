<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InfluencerPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'influencer_id',
        'campaign_type',
        'post_type',
        'price',
        'breakdown',
        'priced_at',
    ];

    protected $casts = [
        'breakdown' => 'array',
        'priced_at' => 'datetime',
        'price' => 'float',
    ];

    public function influencer(): BelongsTo
    {
        return $this->belongsTo(Influencer::class);
    }
}

<?php

declare(strict_types=1);

namespace App\Support;

use InvalidArgumentException;

class CampaignPostType
{
    public static function forCampaignType(string $campaignType): string
    {
        return match ($campaignType) {
            'Reaction Video' => 'reel',
            'Survey', 'Boost Me' => 'story',
            default => throw new InvalidArgumentException("Unknown campaign type: {$campaignType}"),
        };
    }
}

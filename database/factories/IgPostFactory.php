<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\IgPost;
use App\Models\Influencer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends Factory<\App\Models\IgPost> */
class IgPostFactory extends Factory
{
    protected $model = IgPost::class;

    public function definition(): array
    {
        return [
            'influencer_id' => Influencer::factory(),
            'post_type' => $this->faker->randomElement(['story', 'reel', 'feed']),
            'reach' => $this->faker->numberBetween(100, 100000),
            'posted_at' => Carbon::now()->subDays(rand(0, 59)),
        ];
    }
}

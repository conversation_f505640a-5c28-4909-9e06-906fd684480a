<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Influencer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends Factory<\App\Models\Influencer> */
class InfluencerFactory extends Factory
{
    protected $model = Influencer::class;

    public function definition(): array
    {
        return [
            'followers' => $this->faker->numberBetween(1000, 1000000),
            'gamification_percentage' => $this->faker->randomFloat(2, 0, 0.5),
            'flag_story_insight_missing' => false,
            'flag_reel_insight_missing' => false,
            'flag_feed_insight_missing' => false,
        ];
    }
}

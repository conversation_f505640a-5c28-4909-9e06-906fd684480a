<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('influencers', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('followers');
            $table->decimal('gamification_percentage', 5, 2)->default(0);
            $table->unsignedInteger('ig_story_avg_reach')->nullable();
            $table->unsignedInteger('ig_reel_avg_reach')->nullable();
            $table->unsignedInteger('ig_feed_avg_reach')->nullable();
            $table->boolean('flag_story_insight_missing')->default(false);
            $table->boolean('flag_reel_insight_missing')->default(false);
            $table->boolean('flag_feed_insight_missing')->default(false);
            $table->boolean('admin_override_show_hidden')->default(false);
            $table->timestamp('avg_reach_computed_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('influencers');
    }
};

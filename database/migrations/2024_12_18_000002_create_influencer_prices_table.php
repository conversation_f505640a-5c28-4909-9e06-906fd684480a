<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('influencer_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('influencer_id')->constrained('influencers')->cascadeOnDelete();
            $table->string('campaign_type', 64);
            $table->string('post_type', 16);
            $table->decimal('price', 10, 2);
            $table->json('breakdown');
            $table->timestamp('priced_at');
            $table->timestamps();
            $table->unique(['influencer_id', 'campaign_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('influencer_prices');
    }
};

<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\Influencer;
use App\Services\PricingCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingCalculatorTest extends TestCase
{
    use RefreshDatabase;

    private PricingCalculator $calculator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->calculator = new PricingCalculator();
    }

    public function testNormalCalculation(): void
    {
        $inf = Influencer::factory()->create([
            'followers' => 20000,
            'gamification_percentage' => 0.10,
            'ig_story_avg_reach' => 1000,
        ]);

        $result = $this->calculator->calculatePrice($inf, 'Boost Me');
        $this->assertEquals(99.0, $result['price']);
        $this->assertEquals(1000, $result['breakdown']['avg_reach']);
    }

    public function testMissingInsightSkipsAdjustment(): void
    {
        $inf = Influencer::factory()->create([
            'followers' => 20000,
            'gamification_percentage' => 0.0,
            'ig_story_avg_reach' => 500,
            'flag_story_insight_missing' => true,
        ]);
        $result = $this->calculator->calculatePrice($inf, 'Boost Me');
        $this->assertEquals(90.0, $result['price']);
        $this->assertNull($result['breakdown']['reach_multiplier']);
    }

    public function testZeroAverageSkipsAdjustment(): void
    {
        $inf = Influencer::factory()->create([
            'followers' => 20000,
            'gamification_percentage' => 0.0,
            'ig_story_avg_reach' => 0,
        ]);
        $result = $this->calculator->calculatePrice($inf, 'Boost Me');
        $this->assertEquals(90.0, $result['price']);
    }

    public function testHigherLowerAverageAdjustsPrice(): void
    {
        $infHigh = Influencer::factory()->create([
            'followers' => 10000,
            'gamification_percentage' => 0.0,
            'ig_story_avg_reach' => 1200,
        ]);
        $resultHigh = $this->calculator->calculatePrice($infHigh, 'Boost Me');
        $this->assertEquals(100.0, $resultHigh['price']);

        $infLow = Influencer::factory()->create([
            'followers' => 10000,
            'gamification_percentage' => 0.0,
            'ig_story_avg_reach' => 300,
        ]);
        $resultLow = $this->calculator->calculatePrice($infLow, 'Boost Me');
        $this->assertEquals(25.0, $resultLow['price']);
    }

    public function testBoundaryFollowerTiers(): void
    {
        $inf1 = Influencer::factory()->create(['followers' => 1000]);
        $inf2 = Influencer::factory()->create(['followers' => 10000]);
        $inf3 = Influencer::factory()->create(['followers' => 1000000]);

        $tier1 = $this->calculator->calculatePrice($inf1, 'Boost Me')['breakdown']['tier'];
        $tier2 = $this->calculator->calculatePrice($inf2, 'Boost Me')['breakdown']['tier'];
        $tier3 = $this->calculator->calculatePrice($inf3, 'Boost Me')['breakdown']['tier'];

        $this->assertEquals(5.00, $tier1['cpt']);
        $this->assertEquals(4.50, $tier2['cpt']);
        $this->assertEquals(2.00, $tier3['cpt']);
    }
}
